# TopNavigation 二级菜单功能说明

## 概述

根据 `docs/pages/菜单.html` 的设计要求，为 TopNavigation 组件添加了二级菜单功能。二级菜单位于主导航下方，提供更细粒度的导航选项。

## 功能特性

### 1. 独立的二级菜单区域
- 位于主导航栏下方
- 白色背景，与主导航的深色背景形成对比
- 底部有分隔线，视觉层次清晰

### 2. 二级菜单项配置
- 支持图标 + 文字的组合显示
- 使用 Material Icons 图标库
- 支持活跃状态标识
- 支持点击跳转链接

### 3. 交互效果
- 悬停时颜色变化
- 活跃项高亮显示（蓝色）
- 平滑的过渡动画

### 4. 事件处理
- 新增 `onSecondaryMenuClick` 事件
- 支持事件数据传递
- 集成到现有的事件系统中

## 新增属性

### `secondaryMenu`
```typescript
secondaryMenu?: Array<{
  key: string;           // 唯一标识
  label: string;         // 显示文本
  icon?: string;         // Material Icons 图标名称
  href?: string;         // 跳转链接
  active?: boolean;      // 是否为活跃状态
}>;
```

### `showSecondaryMenu`
```typescript
showSecondaryMenu?: boolean; // 是否显示二级菜单区域，默认 false
```

### `onSecondaryMenuClick`
```typescript
onSecondaryMenuClick?: (key: string) => void; // 二级菜单点击回调
```

## 使用示例

```tsx
<TopNavigation
  logo={{
    text: "Quan Zhi Tech",
    showDefaultIcon: true,
    iconText: "全"
  }}
  menu={[
    { key: 'overview', label: '概览' },
    { key: 'apis', label: 'APIs' },
    { key: 'data', label: '数据' }
  ]}
  showSecondaryMenu={true}
  secondaryMenu={[
    { key: 'api', label: 'API', icon: 'api', active: true },
    { key: 'app', label: '应用', icon: 'cloud_upload' }
  ]}
  onMenuClick={(key) => console.log('主菜单点击:', key)}
  onSecondaryMenuClick={(key) => console.log('二级菜单点击:', key)}
/>
```

## 技术实现

### 1. 组件结构调整
- 使用 React Fragment (`<>`) 包装主导航和二级菜单
- 二级菜单作为独立的 div 元素渲染

### 2. 状态管理
- 新增 `activeSecondaryMenu` 状态管理活跃的二级菜单项
- 自动初始化活跃状态（优先使用配置的 active 项）

### 3. 事件系统集成
- 在 `useEventHandler` 中添加 `onSecondaryMenuClick` 方法
- 在事件配置中添加二级菜单点击事件定义

### 4. 样式设计
- 参考 `菜单.html` 的 Tailwind CSS 样式
- 转换为内联样式以保持组件的独立性
- 保持与现有设计系统的一致性

## 文件修改清单

1. **packages/shared/src/components/business/TopNavigation.tsx**
   - 添加二级菜单相关的 props 定义
   - 实现二级菜单渲染逻辑
   - 添加二级菜单点击处理

2. **packages/shared/src/hooks/useEventHandler.ts**
   - 添加 `onSecondaryMenuClick` 事件处理方法

3. **packages/shared/src/components/business/index.ts**
   - 更新组件元数据，添加二级菜单相关属性配置

4. **packages/designer/src/types/events.ts**
   - 添加二级菜单点击事件的配置定义

## 测试验证

创建了 `examples/test-secondary-menu.html` 测试页面，展示：
- 完整的导航结构
- 二级菜单的视觉效果
- 交互功能演示
- 使用示例代码

## 兼容性

- 向后兼容：现有的 TopNavigation 使用不受影响
- 可选功能：通过 `showSecondaryMenu` 控制是否启用
- 渐进增强：在不配置二级菜单时，组件行为与之前完全一致

## 下一步建议

1. 在设计器中添加二级菜单的可视化配置界面
2. 考虑添加二级菜单的响应式设计（移动端适配）
3. 支持更多的二级菜单样式主题
4. 添加二级菜单的键盘导航支持
