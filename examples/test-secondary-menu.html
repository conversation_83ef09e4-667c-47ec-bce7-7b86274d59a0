<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TopNavigation 二级菜单测试</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background-color: #f5f5f5;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 20px auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .demo-title {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .demo-description {
            padding: 15px 20px;
            color: #666;
            line-height: 1.5;
        }
        
        .demo-preview {
            border-bottom: 1px solid #e9ecef;
        }
        
        .demo-code {
            padding: 20px;
            background: #f8f9fa;
        }
        
        .demo-code pre {
            margin: 0;
            padding: 15px;
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 14px;
            line-height: 1.4;
        }
        
        /* 模拟TopNavigation样式 */
        .mock-top-navigation {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            height: 48px;
            background-color: #1f2937;
            color: #ffffff;
        }
        
        .mock-logo-area {
            display: flex;
            align-items: center;
        }
        
        .mock-logo {
            display: flex;
            align-items: center;
            margin-right: 40px;
        }
        
        .mock-logo-icon {
            width: 24px;
            height: 24px;
            background-color: #ffffff;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            font-size: 12px;
            font-weight: bold;
            color: #1f2937;
        }
        
        .mock-logo-text {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
        }
        
        .mock-menu {
            display: flex;
            align-items: center;
            margin-left: 24px;
        }
        
        .mock-menu-item {
            background: transparent;
            border: none;
            color: #d1d5db;
            font-size: 14px;
            padding: 6px 16px;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.2s;
            font-weight: 400;
            margin-right: 4px;
        }
        
        .mock-menu-item.active {
            background: #3b82f6;
            color: #ffffff;
            font-weight: 500;
        }
        
        .mock-menu-item:hover:not(.active) {
            background-color: rgba(255, 255, 255, 0.1);
            color: #ffffff;
        }
        
        .mock-right-area {
            display: flex;
            align-items: center;
        }
        
        .mock-icon-btn {
            background: transparent;
            border: none;
            color: #d1d5db;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .mock-icon-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: #ffffff;
        }
        
        /* 二级菜单样式 */
        .mock-secondary-menu {
            background-color: #ffffff;
            border-bottom: 1px solid #e5e7eb;
            padding: 12px 16px;
        }
        
        .mock-secondary-menu-container {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .mock-secondary-menu-item {
            display: flex;
            align-items: center;
            background: transparent;
            border: none;
            color: #6b7280;
            font-size: 14px;
            font-weight: 400;
            cursor: pointer;
            padding: 4px 0;
            transition: color 0.2s;
        }
        
        .mock-secondary-menu-item.active {
            color: #3b82f6;
            font-weight: 600;
        }
        
        .mock-secondary-menu-item:hover:not(.active) {
            color: #3b82f6;
        }
        
        .mock-secondary-menu-item .material-icons {
            font-size: 20px;
            margin-right: 4px;
        }
        
        .content-area {
            padding: 40px 20px;
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h2 class="demo-title">TopNavigation 二级菜单功能演示</h2>
        
        <div class="demo-description">
            <p>根据 <code>docs/pages/菜单.html</code> 的设计，为 TopNavigation 组件添加了二级菜单功能。</p>
            <p><strong>新增功能：</strong></p>
            <ul>
                <li>独立的二级菜单区域，位于主导航下方</li>
                <li>支持图标和文字的二级菜单项</li>
                <li>活跃状态和悬停效果</li>
                <li>可配置显示/隐藏二级菜单</li>
                <li>二级菜单点击事件处理</li>
            </ul>
        </div>
        
        <div class="demo-preview">
            <!-- 模拟TopNavigation主导航 -->
            <div class="mock-top-navigation">
                <div class="mock-logo-area">
                    <div class="mock-logo">
                        <div class="mock-logo-icon">全</div>
                        <span class="mock-logo-text">Quan Zhi Tech</span>
                    </div>
                    
                    <div class="mock-menu">
                        <button class="mock-menu-item">概览</button>
                        <button class="mock-menu-item active">APIs</button>
                        <button class="mock-menu-item">数据</button>
                        <button class="mock-menu-item">弱点</button>
                        <button class="mock-menu-item">风控</button>
                        <button class="mock-menu-item">审计</button>
                        <button class="mock-menu-item">报告</button>
                        <button class="mock-menu-item">态势</button>
                        <button class="mock-menu-item">管控</button>
                        <button class="mock-menu-item">配置</button>
                    </div>
                </div>
                
                <div class="mock-right-area">
                    <button class="mock-icon-btn">
                        <span class="material-icons">search</span>
                    </button>
                    <button class="mock-icon-btn">
                        <span class="material-icons">notifications</span>
                    </button>
                    <button class="mock-icon-btn">
                        <span class="material-icons">book</span>
                    </button>
                </div>
            </div>
            
            <!-- 二级菜单区域 -->
            <div class="mock-secondary-menu">
                <div class="mock-secondary-menu-container">
                    <button class="mock-secondary-menu-item active">
                        <span class="material-icons">api</span>
                        <span>API</span>
                    </button>
                    <button class="mock-secondary-menu-item">
                        <span class="material-icons">cloud_upload</span>
                        <span>应用</span>
                    </button>
                </div>
            </div>
            
            <div class="content-area">
                <p>这里是页面内容区域</p>
                <p>二级菜单已成功添加到 TopNavigation 组件中</p>
            </div>
        </div>
        
        <div class="demo-code">
            <h3 style="margin-top: 0; color: #333;">组件使用示例：</h3>
            <pre><code>&lt;TopNavigation
  logo={{
    text: "Quan Zhi Tech",
    showDefaultIcon: true,
    iconText: "全"
  }}
  menu={[
    { key: 'overview', label: '概览' },
    { key: 'apis', label: 'APIs' },
    { key: 'data', label: '数据' },
    // ... 更多菜单项
  ]}
  showSecondaryMenu={true}
  secondaryMenu={[
    { key: 'api', label: 'API', icon: 'api', active: true },
    { key: 'app', label: '应用', icon: 'cloud_upload' }
  ]}
  onMenuClick={(key) => console.log('主菜单点击:', key)}
  onSecondaryMenuClick={(key) => console.log('二级菜单点击:', key)}
/&gt;</code></pre>
        </div>
    </div>

    <script>
        // 添加交互效果
        document.querySelectorAll('.mock-menu-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.mock-menu-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });
        
        document.querySelectorAll('.mock-secondary-menu-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.mock-secondary-menu-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
