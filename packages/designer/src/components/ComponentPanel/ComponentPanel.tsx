import React, { useMemo, useState } from 'react';
import { ComponentMeta, createDefaultComponentSchema } from '@lowcode/shared';
import { useDesigner } from '../../context/DesignerContext';
import { ComponentPanelItem } from '../../types';

export interface ComponentPanelProps {
  width?: number;
  style?: React.CSSProperties;
  className?: string;
}

// 模拟组件库数据
const mockComponentLibrary: ComponentPanelItem[] = [
  {
    category: 'business',
    components: [
      {
        type: 'TopNavigation',
        name: '顶部导航',
        description: '顶部导航组件',
        category: 'business',
        icon: '🧭',
        props: [],
        defaultProps: {}
      },
      {
        type: 'SidebarTreeView',
        name: '侧边栏树形导航',
        description: '侧边栏树形导航组件',
        category: 'business',
        icon: '🌳',
        props: [],
        defaultProps: {}
      },
      {
        type: 'TableViewWithSearch',
        name: '表格搜索视图',
        description: '集成搜索、工具栏、表格功能的复合组件',
        category: 'business',
        icon: '📊',
        props: [],
        defaultProps: {}
      },
      {
        type: 'StatusBar',
        name: '状态栏',
        description: '底部状态栏组件',
        category: 'business',
        icon: '📊',
        props: [],
        defaultProps: {}
      }
    ]
  },
  {
    category: 'layout',
    components: [
      {
        type: 'TopDownLayout',
        name: '上下布局',
        description: '上下布局组件',
        category: 'layout',
        icon: '🏗️',
        props: [],
        defaultProps: {}
      }
    ]
  }
];

const categoryNames: Record<string, string> = {
  business: '业务组件',
  layout: '布局组件'
};

export const ComponentPanel: React.FC<ComponentPanelProps> = ({
  width = 280,
  style,
  className
}) => {
  const { addComponent } = useDesigner();
  const [activeCategory, setActiveCategory] = useState<string>('business');
  const [searchValue, setSearchValue] = useState<string>('');

  // 处理组件拖拽开始
  const handleDragStart = (event: React.DragEvent, meta: ComponentMeta) => {
    event.dataTransfer.setData('application/json', JSON.stringify(meta));
    event.dataTransfer.effectAllowed = 'copy';
  };

  // 处理组件点击添加
  const handleComponentClick = (meta: ComponentMeta) => {
    const componentSchema = createDefaultComponentSchema(meta.type, meta);
    addComponent(componentSchema);
  };

  // 过滤组件
  const filteredComponents = useMemo(() => (
    mockComponentLibrary.map(category => ({
      ...category,
      components: category.components.filter(component =>
        component.name.toLowerCase().includes(searchValue.toLowerCase()) ||
        (component.description ?? '').toLowerCase().includes(searchValue.toLowerCase())
      )
    })).filter(category => category.components.length > 0)
  ), [searchValue]);

  const defaultStyle: React.CSSProperties = {
    width: `${width}px`,
    height: '100%',
    backgroundColor: '#fafafa',
    borderRight: '1px solid #e8e8e8',
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
    ...style,
  };

  return (
    <div className={`lowcode-component-panel ${className || ''}`} style={defaultStyle}>
      {/* 标题 */}
      <div style={{
        padding: '16px',
        borderBottom: '1px solid #e8e8e8',
        backgroundColor: '#ffffff'
      }}>
        <h3 style={{ margin: 0, fontSize: '16px', fontWeight: '600' }}>组件库</h3>
      </div>

      {/* 搜索框 */}
      <div style={{ padding: '16px', backgroundColor: '#ffffff', borderBottom: '1px solid #e8e8e8' }}>
        <input
          type="text"
          placeholder="搜索组件..."
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          style={{
            width: '100%',
            padding: '8px 12px',
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            fontSize: '14px',
            outline: 'none'
          }}
        />
      </div>

      {/* 分类标签 */}
      <div style={{
        display: 'flex',
        padding: '8px 16px',
        backgroundColor: '#ffffff',
        borderBottom: '1px solid #e8e8e8',
        gap: '8px'
      }}>
        {Object.keys(categoryNames).map(category => (
          <button
            key={category}
            onClick={() => setActiveCategory(category)}
            style={{
              padding: '4px 12px',
              border: 'none',
              borderRadius: '16px',
              fontSize: '12px',
              cursor: 'pointer',
              backgroundColor: activeCategory === category ? '#1890ff' : '#f0f0f0',
              color: activeCategory === category ? '#ffffff' : '#666666',
              transition: 'all 0.3s'
            }}
          >
            {categoryNames[category]}
          </button>
        ))}
      </div>

      {/* 组件列表 */}
      <div style={{ flex: 1, overflow: 'auto', padding: '16px' }}>
        {filteredComponents
          .filter(category => !activeCategory || category.category === activeCategory)
          .map(category => (
            <div key={category.category} style={{ marginBottom: '24px' }}>
              <h4 style={{
                margin: '0 0 12px 0',
                fontSize: '14px',
                fontWeight: '600',
                color: '#333'
              }}>
                {categoryNames[category.category]}
              </h4>

              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fill, minmax(120px, 1fr))',
                gap: '12px'
              }}>
                {category.components.map(component => (
                  <div
                    key={component.type}
                    draggable
                    onDragStart={(e) => handleDragStart(e, component)}
                    onClick={() => handleComponentClick(component)}
                    style={{
                      padding: '12px',
                      backgroundColor: '#ffffff',
                      border: '1px solid #e8e8e8',
                      borderRadius: '8px',
                      cursor: 'pointer',
                      textAlign: 'center',
                      transition: 'all 0.3s',
                      userSelect: 'none'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.borderColor = '#1890ff';
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(24, 144, 255, 0.2)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.borderColor = '#e8e8e8';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  >
                    <div style={{ fontSize: '24px', marginBottom: '8px' }}>
                      {component.icon}
                    </div>
                    <div style={{
                      fontSize: '12px',
                      fontWeight: '500',
                      color: '#333',
                      marginBottom: '4px'
                    }}>
                      {component.name}
                    </div>
                    <div style={{
                      fontSize: '10px',
                      color: '#999',
                      lineHeight: '1.2'
                    }}>
                      {component.description}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}

        {/* 空状态 */}
        {filteredComponents.length === 0 && (
          <div style={{
            textAlign: 'center',
            padding: '40px 20px',
            color: '#999'
          }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>🔍</div>
            <div>没有找到匹配的组件</div>
          </div>
        )}
      </div>
    </div>
  );
};
