import React from 'react';
import { useEventHandler } from '../../hooks/useEventHandler';

// 模拟视图列表数据 - 实际应用中这些数据会从服务器接口获取
const mockViewListData: TreeNode[] = [
  {
    key: 'api-overview',
    title: 'API概览',
    icon: '📊',
    href: '/api/overview'
  },
  {
    key: 'api-management',
    title: 'API管理',
    icon: '🔗',
    children: [
      { key: 'all-apis', title: '全部API', icon: '📋', href: '/api/all', count: 1247 },
      { key: 'sensitive-apis', title: '敏感API', icon: '🔒', href: '/api/sensitive', count: 89 },
      { key: 'risk-apis', title: '风险API', icon: '⚠️', href: '/api/risk', count: 34 }
    ]
  },
  {
    key: 'data-analysis',
    title: '数据分析',
    icon: '📈',
    children: [
      { key: 'traffic-analysis', title: '流量分析', icon: '🌊', href: '/data/traffic' },
      { key: 'behavior-analysis', title: '行为分析', icon: '👤', href: '/data/behavior' },
      { key: 'security-analysis', title: '安全分析', icon: '🛡️', href: '/data/security' }
    ]
  },
  {
    key: 'security-center',
    title: '安全中心',
    icon: '🔐',
    children: [
      { key: 'vulnerability', title: '漏洞管理', icon: '🐛', href: '/security/vulnerability', count: 12 },
      { key: 'threat-detection', title: '威胁检测', icon: '🎯', href: '/security/threat', count: 5 },
      { key: 'audit-log', title: '审计日志', icon: '📝', href: '/security/audit' }
    ]
  }
];

export interface TreeNode {
  key: string;
  title: string;
  icon?: string;
  href?: string;
  children?: TreeNode[];
  disabled?: boolean;
  count?: number; // 添加计数显示
}

export interface SidebarTreeViewProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onSelect'> {
  data?: TreeNode[]; // 改为可选，组件内部使用模拟数据
  width?: number | string;
  height?: number | string;
  searchable?: boolean;
  searchPlaceholder?: string;
  defaultExpandedKeys?: string[];
  defaultSelectedKeys?: string[];
  showIcon?: boolean;
  style?: React.CSSProperties;
  className?: string;
  onSelect?: (selectedKeys: string[], node: TreeNode) => void;
  onExpand?: (expandedKeys: string[], node: TreeNode) => void;
  onSearch?: (value: string) => void;
  // 组件ID和事件配置
  componentId?: string;
  events?: Record<string, any>;
}

export const SidebarTreeView: React.FC<SidebarTreeViewProps> = ({
  data = mockViewListData, // 使用模拟数据作为默认值
  width = 240,
  height = '100%',
  searchable = true,
  searchPlaceholder = '搜索...',
  defaultExpandedKeys = [],
  defaultSelectedKeys = [],
  showIcon = true,
  style,
  className,
  onSelect,
  onExpand,
  onSearch,
  componentId,
  events,
  ...rest
}) => {
  const [expandedKeys, setExpandedKeys] = React.useState<string[]>(defaultExpandedKeys);
  const [selectedKeys, setSelectedKeys] = React.useState<string[]>(defaultSelectedKeys.length > 0 ? defaultSelectedKeys : ['all']);
  const [searchValue, setSearchValue] = React.useState<string>('');
  const [filteredData, setFilteredData] = React.useState<TreeNode[]>(data);

  // 事件处理
  const eventHandler = useEventHandler(componentId, events);

  // 组件挂载时触发初始加载事件
  React.useEffect(() => {
    eventHandler.onMount({
      componentId,
      timestamp: Date.now()
    });
  }, [componentId, eventHandler]);

  // 搜索功能
  React.useEffect(() => {
    if (!searchValue) {
      setFilteredData(data);
      return;
    }

    const filterTree = (nodes: TreeNode[]): TreeNode[] => {
      return nodes.reduce((acc: TreeNode[], node) => {
        const matchesSearch = node.title.toLowerCase().includes(searchValue.toLowerCase());
        const filteredChildren = node.children ? filterTree(node.children) : [];

        if (matchesSearch || filteredChildren.length > 0) {
          acc.push({
            ...node,
            children: filteredChildren.length > 0 ? filteredChildren : node.children
          });
        }

        return acc;
      }, []);
    };

    const filtered = filterTree(data);
    setFilteredData(filtered);

    // 搜索时自动展开所有匹配的节点
    if (searchValue) {
      const getAllKeys = (nodes: TreeNode[]): string[] => {
        let keys: string[] = [];
        nodes.forEach(node => {
          keys.push(node.key);
          if (node.children) {
            keys = keys.concat(getAllKeys(node.children));
          }
        });
        return keys;
      };
      setExpandedKeys(getAllKeys(filtered));
    }
  }, [searchValue, data]);

  const handleNodeClick = (node: TreeNode) => {
    if (node.disabled) return;

    // 触发节点点击事件
    eventHandler.onNodeClick({
      node,
      componentId,
      timestamp: Date.now()
    });

    // 处理选中状态
    const newSelectedKeys = [node.key];
    setSelectedKeys(newSelectedKeys);
    onSelect?.(newSelectedKeys, node);

    // 如果有链接，进行跳转
    if (node.href) {
      window.location.href = node.href;
    }
  };

  const handleNodeExpand = (node: TreeNode) => {
    const isExpanded = expandedKeys.includes(node.key);
    let newExpandedKeys: string[];

    if (isExpanded) {
      newExpandedKeys = expandedKeys.filter(key => key !== node.key);
    } else {
      newExpandedKeys = [...expandedKeys, node.key];
    }

    // 触发节点展开事件
    eventHandler.onNodeExpand({
      node,
      isExpanded: !isExpanded,
      expandedKeys: newExpandedKeys,
      componentId,
      timestamp: Date.now()
    });

    setExpandedKeys(newExpandedKeys);
    onExpand?.(newExpandedKeys, node);
  };

  const handleSearch = (value: string) => {
    // 触发搜索事件
    eventHandler.onSearch({
      searchValue: value,
      componentId,
      timestamp: Date.now()
    });

    setSearchValue(value);
    onSearch?.(value);
  };

  const renderTreeNode = (node: TreeNode, level: number = 0): React.ReactElement => {
    const isExpanded = expandedKeys.includes(node.key);
    const isSelected = selectedKeys.includes(node.key);
    const hasChildren = node.children && node.children.length > 0;
    const paddingLeft = level * 16 + 16;

    return (
      <div key={node.key}>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '6px 12px',
            paddingLeft: `${paddingLeft}px`,
            cursor: node.disabled ? 'not-allowed' : 'pointer',
            backgroundColor: isSelected ? '#e6f7ff' : 'transparent',
            color: node.disabled ? '#bfbfbf' : isSelected ? '#1890ff' : '#333333',
            transition: 'all 0.2s ease',
            fontSize: '13px',
            userSelect: 'none',
            minHeight: '32px',
            borderRadius: level === 0 ? '0' : '4px',
            margin: level === 0 ? '0' : '0 6px',
            position: 'relative'
          }}
          onClick={() => handleNodeClick(node)}
          onMouseEnter={(e) => {
            if (!node.disabled && !isSelected) {
              e.currentTarget.style.backgroundColor = '#f5f5f5';
            }
          }}
          onMouseLeave={(e) => {
            if (!node.disabled && !isSelected) {
              e.currentTarget.style.backgroundColor = 'transparent';
            }
          }}
        >
          {/* 左侧内容 */}
          <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
            {/* 展开/收起图标 */}
            {hasChildren ? (
              <span
                style={{
                  fontFamily: 'Material Icons Outlined',
                  marginRight: '6px',
                  fontSize: '16px',
                  transition: 'transform 0.3s ease',
                  transform: isExpanded ? 'rotate(90deg)' : 'rotate(0deg)',
                  cursor: 'pointer',
                  color: '#666666',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '18px',
                  height: '18px',
                  borderRadius: '3px'
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  handleNodeExpand(node);
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#f0f0f0';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                chevron_right
              </span>
            ) : (
              <span style={{ marginRight: '24px' }} />
            )}

            {/* 节点图标 */}
            {showIcon && node.icon && (
              <span style={{ marginRight: '6px', fontSize: '14px' }}>
                {node.icon}
              </span>
            )}

            {/* 节点标题 */}
            <span style={{
              fontWeight: isSelected ? '600' : '400',
              color: isSelected ? '#1890ff' : '#333333',
              fontSize: '13px',
              lineHeight: '18px'
            }}>
              {node.title}
            </span>

            {/* 信息图标（如果是选中的全部API项） */}
            {isSelected && node.key === 'all' && (
              <span
                style={{
                  fontFamily: 'Material Icons Outlined',
                  marginLeft: '4px',
                  fontSize: '14px',
                  color: '#1890ff',
                  opacity: 0.8
                }}
              >
                info
              </span>
            )}
          </div>

          {/* 右侧计数 */}
          {typeof node.count === 'number' && (
            <span style={{
              fontSize: '12px',
              color: isSelected ? '#1890ff' : '#999999',
              fontWeight: '500',
              marginLeft: '6px',
              backgroundColor: isSelected ? 'rgba(24, 144, 255, 0.1)' : '#f5f5f5',
              padding: '1px 6px',
              borderRadius: '10px',
              minWidth: '18px',
              textAlign: 'center',
              lineHeight: '16px'
            }}>
              {node.count}
            </span>
          )}
        </div>

        {/* 子节点 */}
        {hasChildren && isExpanded && (
          <div>
            {node.children!.map(child => renderTreeNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  const defaultStyle: React.CSSProperties = {
    width,
    height,
    backgroundColor: '#ffffff',
    borderRight: '1px solid #f0f0f0',
    overflow: 'hidden',
    display: 'flex',
    flexDirection: 'column',
    boxShadow: '2px 0 8px rgba(0, 0, 0, 0.06)',
    ...style,
  };

  return (
    <div className={`lowcode-sidebar-tree-view ${className || ''}`} style={defaultStyle} {...rest}>
      {/* 搜索框 */}
      {searchable && (
        <div style={{
          padding: '16px 12px 12px 12px',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <div style={{ position: 'relative' }}>
            <input
              type="text"
              placeholder={searchPlaceholder}
              value={searchValue}
              onChange={(e) => handleSearch(e.target.value)}
              style={{
                width: '100%',
                height: '32px',
                padding: '8px 36px 8px 12px',
                border: '1px solid #e0e0e0',
                borderRadius: '16px',
                fontSize: '13px',
                outline: 'none',
                background: '#f8f9fa',
                transition: 'all 0.3s ease',
                color: '#333333',
                boxSizing: 'border-box'
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = '#1890ff';
                e.currentTarget.style.background = '#ffffff';
                e.currentTarget.style.boxShadow = '0 2px 8px rgba(24, 144, 255, 0.15)';
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = '#e0e0e0';
                e.currentTarget.style.background = '#f8f9fa';
                e.currentTarget.style.boxShadow = 'none';
              }}
            />
            {/* 搜索图标 */}
            <span
              style={{
                fontFamily: 'Material Icons Outlined',
                position: 'absolute',
                right: '10px',
                top: '50%',
                transform: 'translateY(-50%)',
                fontSize: '16px',
                color: '#999999',
                pointerEvents: 'none'
              }}
            >
              search
            </span>
          </div>
        </div>
      )}

      {/* 树形结构 */}
      <div style={{
        flex: 1,
        overflow: 'auto',
        paddingTop: '4px'
      }}>
        {filteredData.length > 0 ? (
          filteredData.map(node => renderTreeNode(node))
        ) : (
          <div
            style={{
              padding: '40px 20px',
              textAlign: 'center',
              color: '#cccccc',
              fontSize: '14px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '8px'
            }}
          >
            <span style={{ fontFamily: 'Material Icons Outlined', fontSize: '32px', opacity: 0.5 }}>
              {searchValue ? 'search_off' : 'folder_open'}
            </span>
            <span>
              {searchValue ? '没有找到匹配的结果' : '暂无数据'}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};
